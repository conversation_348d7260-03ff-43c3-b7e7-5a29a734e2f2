import { rest } from 'msw';

const baseURL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api/v1';

export const handlers = [
  // Login endpoint
  rest.post(`${baseURL}/auth/login`, (req, res, ctx) => {
    const { email, password } = req.body;

    // Mock successful login
    if (email === '<EMAIL>' && password === 'password123') {
      return res(
        ctx.status(200),
        ctx.json({
          token: 'mock-jwt-token',
          user: {
            id: 1,
            email: '<EMAIL>',
            name: 'Test User',
          },
        })
      );
    }

    // Mock invalid credentials
    return res(
      ctx.status(401),
      ctx.json({
        message: 'Invalid email or password',
      })
    );
  }),

  // Signup endpoint
  rest.post(`${baseURL}/auth/signup`, (req, res, ctx) => {
    const { email, password, name } = req.body;

    // Mock email already exists
    if (email === '<EMAIL>') {
      return res(
        ctx.status(400),
        ctx.json({
          message: 'Email already exists',
        })
      );
    }

    // Mock successful signup
    return res(
      ctx.status(201),
      ctx.json({
        message: 'User created successfully',
        user: {
          id: 2,
          email,
          name,
        },
      })
    );
  }),

  // Forgot password endpoint
  rest.post(`${baseURL}/auth/forgot-password`, (req, res, ctx) => {
    const { email } = req.body;

    // Mock user not found
    if (email === '<EMAIL>') {
      return res(
        ctx.status(404),
        ctx.json({
          message: 'User not found',
        })
      );
    }

    // Mock successful password reset request
    return res(
      ctx.status(200),
      ctx.json({
        message: 'Password reset email sent successfully',
      })
    );
  }),

  // Get profile endpoint
  rest.get(`${baseURL}/auth/profile`, (req, res, ctx) => {
    const authHeader = req.headers.get('Authorization');

    // Mock unauthorized access
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res(
        ctx.status(401),
        ctx.json({
          message: 'Unauthorized',
        })
      );
    }

    const token = authHeader.replace('Bearer ', '');

    // Mock invalid token
    if (token !== 'mock-jwt-token') {
      return res(
        ctx.status(401),
        ctx.json({
          message: 'Invalid token',
        })
      );
    }

    // Mock successful profile fetch
    return res(
      ctx.status(200),
      ctx.json({
        id: 1,
        email: '<EMAIL>',
        name: 'Test User',
        createdAt: '2023-01-01T00:00:00.000Z',
      })
    );
  }),

  // Mock server error for testing
  rest.post(`${baseURL}/auth/server-error`, (req, res, ctx) => {
    return res(
      ctx.status(500),
      ctx.json({
        message: 'Internal server error',
      })
    );
  }),
];
