import React from 'react';
import { render, screen } from '@testing-library/react';
import { <PERSON>rowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import PrivateRoute from './PrivateRoute';
import { AuthProvider } from '../../context/AuthContext';

const theme = createTheme();

// Mock the auth API
jest.mock('../../api/auth', () => ({
  authAPI: {
    getToken: jest.fn(),
    getProfile: jest.fn(),
    logout: jest.fn(),
  },
}));

const TestWrapper = ({ children, authState = {} }) => {
  // Mock AuthProvider with custom state
  const MockAuthProvider = ({ children }) => {
    const mockAuthContext = {
      isAuthenticated: false,
      loading: false,
      user: null,
      login: jest.fn(),
      signup: jest.fn(),
      logout: jest.fn(),
      forgotPassword: jest.fn(),
      ...authState,
    };

    return (
      <div data-testid="mock-auth-provider">
        {React.cloneElement(children, { mockAuthContext })}
      </div>
    );
  };

  return (
    <ThemeProvider theme={theme}>
      <BrowserRouter>
        <MockAuthProvider>
          {children}
        </MockAuthProvider>
      </BrowserRouter>
    </ThemeProvider>
  );
};

// Override useAuth hook for testing
const mockUseAuth = (authState) => {
  const originalModule = jest.requireActual('../../context/AuthContext');
  return {
    ...originalModule,
    useAuth: () => authState,
  };
};

describe('PrivateRoute', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('shows loading spinner when loading is true', () => {
    jest.doMock('../../context/AuthContext', () => 
      mockUseAuth({ isAuthenticated: false, loading: true })
    );
    
    const PrivateRouteComponent = require('./PrivateRoute').default;
    
    render(
      <TestWrapper>
        <PrivateRouteComponent>
          <div>Protected Content</div>
        </PrivateRouteComponent>
      </TestWrapper>
    );

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
  });

  it('renders children when authenticated', () => {
    jest.doMock('../../context/AuthContext', () => 
      mockUseAuth({ isAuthenticated: true, loading: false })
    );
    
    const PrivateRouteComponent = require('./PrivateRoute').default;
    
    render(
      <TestWrapper>
        <PrivateRouteComponent>
          <div>Protected Content</div>
        </PrivateRouteComponent>
      </TestWrapper>
    );

    expect(screen.getByText('Protected Content')).toBeInTheDocument();
    expect(screen.queryByRole('progressbar')).not.toBeInTheDocument();
  });

  it('redirects to login when not authenticated', () => {
    jest.doMock('../../context/AuthContext', () => 
      mockUseAuth({ isAuthenticated: false, loading: false })
    );
    
    const PrivateRouteComponent = require('./PrivateRoute').default;
    
    render(
      <TestWrapper>
        <PrivateRouteComponent>
          <div>Protected Content</div>
        </PrivateRouteComponent>
      </TestWrapper>
    );

    expect(screen.queryByText('Protected Content')).not.toBeInTheDocument();
    // Note: In a real test environment, you would check for navigation
    // This is a simplified test for the component structure
  });
});
