import React from 'react';
import { render, screen } from '@testing-library/react';
import { useForm } from 'react-hook-form';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import TextFieldWrapper from './TextFieldWrapper';

const theme = createTheme();

// Test component wrapper
const TestWrapper = ({ defaultValues = {}, children }) => {
  const { control } = useForm({ defaultValues });
  
  return (
    <ThemeProvider theme={theme}>
      {React.cloneElement(children, { control })}
    </ThemeProvider>
  );
};

describe('TextFieldWrapper', () => {
  it('renders with basic props', () => {
    render(
      <TestWrapper>
        <TextFieldWrapper
          name="testField"
          label="Test Label"
          placeholder="Test placeholder"
        />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Test Label')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Test placeholder')).toBeInTheDocument();
  });

  it('renders with default value', () => {
    render(
      <TestWrapper defaultValues={{ testField: 'default value' }}>
        <TextFieldWrapper
          name="testField"
          label="Test Label"
        />
      </TestWrapper>
    );

    expect(screen.getByDisplayValue('default value')).toBeInTheDocument();
  });

  it('renders as password field', () => {
    render(
      <TestWrapper>
        <TextFieldWrapper
          name="password"
          label="Password"
          type="password"
        />
      </TestWrapper>
    );

    const passwordField = screen.getByLabelText('Password');
    expect(passwordField).toHaveAttribute('type', 'password');
  });

  it('renders as multiline field', () => {
    render(
      <TestWrapper>
        <TextFieldWrapper
          name="description"
          label="Description"
          multiline
          rows={4}
        />
      </TestWrapper>
    );

    const textArea = screen.getByLabelText('Description');
    expect(textArea).toBeInTheDocument();
    expect(textArea.tagName).toBe('TEXTAREA');
  });

  it('renders in disabled state', () => {
    render(
      <TestWrapper>
        <TextFieldWrapper
          name="disabledField"
          label="Disabled Field"
          disabled
        />
      </TestWrapper>
    );

    expect(screen.getByLabelText('Disabled Field')).toBeDisabled();
  });

  it('applies custom props', () => {
    render(
      <TestWrapper>
        <TextFieldWrapper
          name="customField"
          label="Custom Field"
          data-testid="custom-field"
          margin="dense"
          variant="filled"
        />
      </TestWrapper>
    );

    const field = screen.getByTestId('custom-field');
    expect(field).toBeInTheDocument();
  });
});
