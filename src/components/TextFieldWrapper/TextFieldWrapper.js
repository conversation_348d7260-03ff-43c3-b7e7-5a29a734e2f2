import React from 'react';
import { TextField } from '@mui/material';
import { Controller } from 'react-hook-form';

const TextFieldWrapper = ({
  name,
  control,
  label,
  type = 'text',
  placeholder,
  multiline = false,
  rows = 1,
  disabled = false,
  fullWidth = true,
  margin = 'normal',
  variant = 'outlined',
  ...otherProps
}) => {
  return (
    <Controller
      name={name}
      control={control}
      render={({ field, fieldState: { error } }) => (
        <TextField
          {...field}
          label={label}
          type={type}
          placeholder={placeholder}
          multiline={multiline}
          rows={rows}
          disabled={disabled}
          fullWidth={fullWidth}
          margin={margin}
          variant={variant}
          error={!!error}
          helperText={error?.message}
          {...otherProps}
        />
      )}
    />
  );
};

export default TextFieldWrapper;
