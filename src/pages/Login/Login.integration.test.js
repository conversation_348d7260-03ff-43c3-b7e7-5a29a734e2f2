import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { AuthProvider } from '../../context/AuthContext';
import Login from './Login';
import { server } from '../../mocks/server';
import { rest } from 'msw';

const theme = createTheme();

// Mock react-router-dom navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ state: null }),
}));

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <BrowserRouter>
      <AuthProvider>
        {children}
      </AuthProvider>
    </BrowserRouter>
  </ThemeProvider>
);

describe('Login Component - Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  it('successfully logs in with valid credentials', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Login />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Enter valid credentials (as defined in MSW handlers)
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'password123');
    await user.click(submitButton);

    // Wait for the login process to complete
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/profile', { replace: true });
    });

    // Check that token is stored in localStorage
    expect(localStorage.getItem('authToken')).toBe('mock-jwt-token');
  });

  it('displays error message for invalid credentials', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Login />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    // Enter invalid credentials
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'wrongpassword');
    await user.click(submitButton);

    // Wait for error message to appear
    await waitFor(() => {
      expect(screen.getByText(/invalid email or password/i)).toBeInTheDocument();
    });

    // Ensure no token is stored
    expect(localStorage.getItem('authToken')).toBeNull();
  });

  it('handles server error gracefully', async () => {
    const user = userEvent.setup();
    
    // Override the login endpoint to return server error
    server.use(
      rest.post('http://localhost:8080/api/v1/auth/login', (req, res, ctx) => {
        return res(
          ctx.status(500),
          ctx.json({ message: 'Internal server error' })
        );
      })
    );
    
    render(
      <TestWrapper>
        <Login />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/internal server error/i)).toBeInTheDocument();
    });
  });

  it('shows loading state during login process', async () => {
    const user = userEvent.setup();
    
    // Override the login endpoint to add delay
    server.use(
      rest.post('http://localhost:8080/api/v1/auth/login', (req, res, ctx) => {
        return res(
          ctx.delay(1000),
          ctx.status(200),
          ctx.json({
            token: 'mock-jwt-token',
            user: { id: 1, email: '<EMAIL>', name: 'Test User' },
          })
        );
      })
    );
    
    render(
      <TestWrapper>
        <Login />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'password123');
    await user.click(submitButton);

    // Check for loading indicator
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
  });

  it('redirects to intended page after login', async () => {
    const user = userEvent.setup();
    
    // Mock location state with intended destination
    jest.doMock('react-router-dom', () => ({
      ...jest.requireActual('react-router-dom'),
      useNavigate: () => mockNavigate,
      useLocation: () => ({ 
        state: { from: { pathname: '/dashboard' } } 
      }),
    }));
    
    const LoginComponent = require('./Login').default;
    
    render(
      <TestWrapper>
        <LoginComponent />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/password/i);
    const submitButton = screen.getByRole('button', { name: /sign in/i });

    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true });
    });
  });
});
