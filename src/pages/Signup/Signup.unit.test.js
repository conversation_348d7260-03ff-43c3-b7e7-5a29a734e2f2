import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Signup from './Signup';

const theme = createTheme();

// Mock the auth context
const mockSignup = jest.fn();
const mockAuthContext = {
  signup: mockSignup,
  loading: false,
  isAuthenticated: false,
  user: null,
  login: jest.fn(),
  logout: jest.fn(),
  forgotPassword: jest.fn(),
};

jest.mock('../../context/AuthContext', () => ({
  useAuth: () => mockAuthContext,
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </ThemeProvider>
);

describe('Signup Component - Unit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders signup form with all required fields', () => {
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    expect(screen.getByRole('heading', { name: /sign up/i })).toBeInTheDocument();
    expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/^password$/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/confirm password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign up/i })).toBeInTheDocument();
  });

  it('displays validation errors for empty fields', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const submitButton = screen.getByRole('button', { name: /sign up/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/name is required/i)).toBeInTheDocument();
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password is required/i)).toBeInTheDocument();
    });
  });

  it('displays validation error for invalid email format', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    await user.type(emailField, 'invalid-email');

    const submitButton = screen.getByRole('button', { name: /sign up/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
    });
  });

  it('displays validation error for short name', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText(/full name/i);
    await user.type(nameField, 'A');

    const submitButton = screen.getByRole('button', { name: /sign up/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/name must be at least 2 characters/i)).toBeInTheDocument();
    });
  });

  it('displays validation error for weak password', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const passwordField = screen.getByLabelText(/^password$/i);
    await user.type(passwordField, 'weakpass');

    const submitButton = screen.getByRole('button', { name: /sign up/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/password must contain at least one uppercase letter/i)).toBeInTheDocument();
    });
  });

  it('displays validation error for mismatched passwords', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    
    await user.type(passwordField, 'Password123');
    await user.type(confirmPasswordField, 'DifferentPassword123');

    const submitButton = screen.getByRole('button', { name: /sign up/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/passwords must match/i)).toBeInTheDocument();
    });
  });

  it('calls signup function with correct data on valid form submission', async () => {
    const user = userEvent.setup();
    mockSignup.mockResolvedValue({ success: true });
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText(/full name/i);
    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    await user.type(nameField, 'John Doe');
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'Password123');
    await user.type(confirmPasswordField, 'Password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockSignup).toHaveBeenCalledWith({
        name: 'John Doe',
        email: '<EMAIL>',
        password: 'Password123',
      });
    });
  });

  it('displays success message and redirects after successful signup', async () => {
    const user = userEvent.setup();
    mockSignup.mockResolvedValue({ success: true });
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText(/full name/i);
    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    await user.type(nameField, 'John Doe');
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'Password123');
    await user.type(confirmPasswordField, 'Password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/account created successfully/i)).toBeInTheDocument();
    });
  });

  it('displays error message when signup fails', async () => {
    const user = userEvent.setup();
    const errorMessage = 'Email already exists';
    mockSignup.mockRejectedValue(new Error(errorMessage));
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText(/full name/i);
    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    await user.type(nameField, 'John Doe');
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'Password123');
    await user.type(confirmPasswordField, 'Password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('contains link to login page', () => {
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    expect(screen.getByText(/already have an account\? sign in/i)).toBeInTheDocument();
  });
});
