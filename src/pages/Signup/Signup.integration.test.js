import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { AuthProvider } from '../../context/AuthContext';
import Signup from './Signup';
import { server } from '../../mocks/server';
import { rest } from 'msw';

const theme = createTheme();

// Mock react-router-dom navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <BrowserRouter>
      <AuthProvider>
        {children}
      </AuthProvider>
    </BrowserRouter>
  </ThemeProvider>
);

describe('Signup Component - Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  it('successfully creates account with valid data', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText(/full name/i);
    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    // Enter valid signup data
    await user.type(nameField, 'John Doe');
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'Password123');
    await user.type(confirmPasswordField, 'Password123');
    await user.click(submitButton);

    // Wait for success message
    await waitFor(() => {
      expect(screen.getByText(/account created successfully/i)).toBeInTheDocument();
    });

    // Wait for navigation to login page
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/login');
    }, { timeout: 3000 });
  });

  it('displays error message for existing email', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText(/full name/i);
    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    // Enter data with existing email (as defined in MSW handlers)
    await user.type(nameField, 'John Doe');
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'Password123');
    await user.type(confirmPasswordField, 'Password123');
    await user.click(submitButton);

    // Wait for error message to appear
    await waitFor(() => {
      expect(screen.getByText(/email already exists/i)).toBeInTheDocument();
    });

    // Ensure no navigation occurred
    expect(mockNavigate).not.toHaveBeenCalled();
  });

  it('handles server error gracefully', async () => {
    const user = userEvent.setup();
    
    // Override the signup endpoint to return server error
    server.use(
      rest.post('http://localhost:8080/api/v1/auth/signup', (req, res, ctx) => {
        return res(
          ctx.status(500),
          ctx.json({ message: 'Internal server error' })
        );
      })
    );
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText(/full name/i);
    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    await user.type(nameField, 'John Doe');
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'Password123');
    await user.type(confirmPasswordField, 'Password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/internal server error/i)).toBeInTheDocument();
    });
  });

  it('shows loading state during signup process', async () => {
    const user = userEvent.setup();
    
    // Override the signup endpoint to add delay
    server.use(
      rest.post('http://localhost:8080/api/v1/auth/signup', (req, res, ctx) => {
        return res(
          ctx.delay(1000),
          ctx.status(201),
          ctx.json({
            message: 'User created successfully',
            user: { id: 2, email: '<EMAIL>', name: 'John Doe' },
          })
        );
      })
    );
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText(/full name/i);
    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    await user.type(nameField, 'John Doe');
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'Password123');
    await user.type(confirmPasswordField, 'Password123');
    await user.click(submitButton);

    // Check for loading indicator
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
  });

  it('validates password strength requirements', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const passwordField = screen.getByLabelText(/^password$/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    // Test weak password
    await user.type(passwordField, 'weakpassword');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/password must contain at least one uppercase letter/i)).toBeInTheDocument();
    });
  });

  it('clears error message when user starts typing after error', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Signup />
      </TestWrapper>
    );

    const nameField = screen.getByLabelText(/full name/i);
    const emailField = screen.getByLabelText(/email address/i);
    const passwordField = screen.getByLabelText(/^password$/i);
    const confirmPasswordField = screen.getByLabelText(/confirm password/i);
    const submitButton = screen.getByRole('button', { name: /sign up/i });

    // First, trigger an error
    await user.type(nameField, 'John Doe');
    await user.type(emailField, '<EMAIL>');
    await user.type(passwordField, 'Password123');
    await user.type(confirmPasswordField, 'Password123');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/email already exists/i)).toBeInTheDocument();
    });

    // Then change the email and submit again
    await user.clear(emailField);
    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    // Error should be cleared and success message should appear
    await waitFor(() => {
      expect(screen.queryByText(/email already exists/i)).not.toBeInTheDocument();
      expect(screen.getByText(/account created successfully/i)).toBeInTheDocument();
    });
  });
});
