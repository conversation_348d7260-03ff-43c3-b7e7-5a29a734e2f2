import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { AuthProvider } from '../../context/AuthContext';
import Profile from './Profile';
import { server } from '../../mocks/server';
import { rest } from 'msw';

const theme = createTheme();

// Mock react-router-dom navigation
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <BrowserRouter>
      <AuthProvider>
        {children}
      </AuthProvider>
    </BrowserRouter>
  </ThemeProvider>
);

describe('Profile Component - Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
    // Set up a valid token for authenticated requests
    localStorage.setItem('authToken', 'mock-jwt-token');
  });

  it('loads and displays user profile data from API', async () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    // Wait for profile data to load
    await waitFor(() => {
      expect(screen.getByText(/welcome, test user!/i)).toBeInTheDocument();
    });

    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // User ID
  });

  it('handles logout flow correctly', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    // Wait for profile to load
    await waitFor(() => {
      expect(screen.getByText(/welcome, test user!/i)).toBeInTheDocument();
    });

    // Click logout button
    const logoutButton = screen.getByRole('button', { name: /logout/i });
    await user.click(logoutButton);

    // Check that token is removed from localStorage
    expect(localStorage.getItem('authToken')).toBeNull();
    
    // Check navigation to login page
    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });

  it('handles unauthorized access gracefully', async () => {
    // Remove token to simulate unauthorized access
    localStorage.removeItem('authToken');
    
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    // Should show loading initially, then redirect due to unauthorized access
    expect(screen.getByText(/loading profile.../i)).toBeInTheDocument();
  });

  it('handles API error when fetching profile', async () => {
    // Override the profile endpoint to return error
    server.use(
      rest.get('http://localhost:8080/api/v1/auth/profile', (req, res, ctx) => {
        return res(
          ctx.status(500),
          ctx.json({ message: 'Internal server error' })
        );
      })
    );
    
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    // Should handle error gracefully and potentially redirect to login
    await waitFor(() => {
      // The AuthContext should handle the error and clear the user state
      expect(localStorage.getItem('authToken')).toBeNull();
    });
  });

  it('handles invalid token gracefully', async () => {
    // Set an invalid token
    localStorage.setItem('authToken', 'invalid-token');
    
    // Override the profile endpoint to return unauthorized
    server.use(
      rest.get('http://localhost:8080/api/v1/auth/profile', (req, res, ctx) => {
        return res(
          ctx.status(401),
          ctx.json({ message: 'Invalid token' })
        );
      })
    );
    
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    // Should handle invalid token and clear authentication
    await waitFor(() => {
      expect(localStorage.getItem('authToken')).toBeNull();
    });
  });

  it('displays formatted creation date correctly', async () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText(/welcome, test user!/i)).toBeInTheDocument();
    });

    // Check for formatted creation date
    const expectedDate = new Date('2023-01-01T00:00:00.000Z').toLocaleDateString();
    expect(screen.getByText(expectedDate)).toBeInTheDocument();
  });

  it('shows current date as last login', async () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText(/welcome, test user!/i)).toBeInTheDocument();
    });

    const currentDate = new Date().toLocaleDateString();
    expect(screen.getByText(`Last login: ${currentDate}`)).toBeInTheDocument();
  });

  it('maintains authentication state across component re-renders', async () => {
    const { rerender } = render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText(/welcome, test user!/i)).toBeInTheDocument();
    });

    // Re-render the component
    rerender(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    // Should still show user data without additional API calls
    expect(screen.getByText(/welcome, test user!/i)).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
  });

  it('handles slow API response gracefully', async () => {
    // Override the profile endpoint to add delay
    server.use(
      rest.get('http://localhost:8080/api/v1/auth/profile', (req, res, ctx) => {
        return res(
          ctx.delay(2000),
          ctx.status(200),
          ctx.json({
            id: 1,
            email: '<EMAIL>',
            name: 'Test User',
            createdAt: '2023-01-01T00:00:00.000Z',
          })
        );
      })
    );
    
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    // Should show loading state initially
    expect(screen.getByText(/loading profile.../i)).toBeInTheDocument();

    // Wait for profile data to load
    await waitFor(() => {
      expect(screen.getByText(/welcome, test user!/i)).toBeInTheDocument();
    }, { timeout: 3000 });
  });

  it('displays user avatar with correct initial', async () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText(/welcome, test user!/i)).toBeInTheDocument();
    });

    // Check for avatar with first letter of name
    expect(screen.getByText('T')).toBeInTheDocument(); // First letter of "Test User"
  });
});
