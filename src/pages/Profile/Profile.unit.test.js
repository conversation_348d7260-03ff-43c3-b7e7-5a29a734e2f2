import React from 'react';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import Profile from './Profile';

const theme = createTheme();

// Mock the auth context
const mockLogout = jest.fn();
const mockAuthContext = {
  logout: mockLogout,
  loading: false,
  isAuthenticated: true,
  user: {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    createdAt: '2023-01-01T00:00:00.000Z',
  },
  login: jest.fn(),
  signup: jest.fn(),
  forgotPassword: jest.fn(),
};

jest.mock('../../context/AuthContext', () => ({
  useAuth: () => mockAuthContext,
}));

// Mock react-router-dom
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </ThemeProvider>
);

describe('Profile Component - Unit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders profile page with user information', () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    expect(screen.getByText(/welcome, john doe!/i)).toBeInTheDocument();
    expect(screen.getByText(/manage your account information/i)).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // User ID
  });

  it('displays user avatar with first letter of name', () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    // Check for avatar with first letter
    const avatar = screen.getByText('J'); // First letter of "John Doe"
    expect(avatar).toBeInTheDocument();
  });

  it('displays formatted creation date', () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    // Check for formatted date
    const expectedDate = new Date('2023-01-01T00:00:00.000Z').toLocaleDateString();
    expect(screen.getByText(expectedDate)).toBeInTheDocument();
  });

  it('calls logout function and navigates to login when logout button is clicked', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    const logoutButton = screen.getByRole('button', { name: /logout/i });
    await user.click(logoutButton);

    expect(mockLogout).toHaveBeenCalledTimes(1);
    expect(mockNavigate).toHaveBeenCalledWith('/login');
  });

  it('shows edit profile button as disabled', () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    const editButton = screen.getByRole('button', { name: /edit profile/i });
    expect(editButton).toBeDisabled();
  });

  it('displays current date as last login', () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    const currentDate = new Date().toLocaleDateString();
    expect(screen.getByText(`Last login: ${currentDate}`)).toBeInTheDocument();
  });

  it('renders personal information section', () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    expect(screen.getByText(/personal information/i)).toBeInTheDocument();
    expect(screen.getByText(/full name/i)).toBeInTheDocument();
    expect(screen.getByText(/user id/i)).toBeInTheDocument();
  });

  it('renders contact information section', () => {
    render(
      <TestWrapper>
        <Profile />
      </TestWrapper>
    );

    expect(screen.getByText(/contact information/i)).toBeInTheDocument();
    expect(screen.getByText(/email address/i)).toBeInTheDocument();
    expect(screen.getByText(/account created/i)).toBeInTheDocument();
  });
});

describe('Profile Component - Edge Cases', () => {
  it('handles user with no name gracefully', () => {
    const mockAuthContextNoName = {
      ...mockAuthContext,
      user: {
        id: 1,
        name: null,
        email: '<EMAIL>',
        createdAt: '2023-01-01T00:00:00.000Z',
      },
    };

    jest.doMock('../../context/AuthContext', () => ({
      useAuth: () => mockAuthContextNoName,
    }));

    const ProfileComponent = require('./Profile').default;

    render(
      <TestWrapper>
        <ProfileComponent />
      </TestWrapper>
    );

    expect(screen.getByText(/welcome, user!/i)).toBeInTheDocument();
    expect(screen.getByText(/not provided/i)).toBeInTheDocument();
    expect(screen.getByText('U')).toBeInTheDocument(); // Default avatar letter
  });

  it('handles user with no email gracefully', () => {
    const mockAuthContextNoEmail = {
      ...mockAuthContext,
      user: {
        id: 1,
        name: 'John Doe',
        email: null,
        createdAt: '2023-01-01T00:00:00.000Z',
      },
    };

    jest.doMock('../../context/AuthContext', () => ({
      useAuth: () => mockAuthContextNoEmail,
    }));

    const ProfileComponent = require('./Profile').default;

    render(
      <TestWrapper>
        <ProfileComponent />
      </TestWrapper>
    );

    expect(screen.getByText(/not provided/i)).toBeInTheDocument();
  });

  it('handles user with no creation date gracefully', () => {
    const mockAuthContextNoDate = {
      ...mockAuthContext,
      user: {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        createdAt: null,
      },
    };

    jest.doMock('../../context/AuthContext', () => ({
      useAuth: () => mockAuthContextNoDate,
    }));

    const ProfileComponent = require('./Profile').default;

    render(
      <TestWrapper>
        <ProfileComponent />
      </TestWrapper>
    );

    expect(screen.getByText('N/A')).toBeInTheDocument();
  });

  it('shows loading state when user is null', () => {
    const mockAuthContextNoUser = {
      ...mockAuthContext,
      user: null,
    };

    jest.doMock('../../context/AuthContext', () => ({
      useAuth: () => mockAuthContextNoUser,
    }));

    const ProfileComponent = require('./Profile').default;

    render(
      <TestWrapper>
        <ProfileComponent />
      </TestWrapper>
    );

    expect(screen.getByText(/loading profile.../i)).toBeInTheDocument();
  });
});
