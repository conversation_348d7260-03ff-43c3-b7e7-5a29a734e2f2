import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { AuthProvider } from '../../context/AuthContext';
import ForgotPassword from './ForgotPassword';
import { server } from '../../mocks/server';
import { rest } from 'msw';

const theme = createTheme();

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <BrowserRouter>
      <AuthProvider>
        {children}
      </AuthProvider>
    </BrowserRouter>
  </ThemeProvider>
);

describe('ForgotPassword Component - Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  it('successfully sends password reset email with valid email', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    // Enter valid email
    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    // Wait for success message
    await waitFor(() => {
      expect(screen.getByText(/password reset instructions have been sent/i)).toBeInTheDocument();
    });
  });

  it('displays error message for non-existent email', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    // Enter non-existent email (as defined in MSW handlers)
    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    // Wait for error message to appear
    await waitFor(() => {
      expect(screen.getByText(/user not found/i)).toBeInTheDocument();
    });
  });

  it('handles server error gracefully', async () => {
    const user = userEvent.setup();
    
    // Override the forgot password endpoint to return server error
    server.use(
      rest.post('http://localhost:8080/api/v1/auth/forgot-password', (req, res, ctx) => {
        return res(
          ctx.status(500),
          ctx.json({ message: 'Internal server error' })
        );
      })
    );
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/internal server error/i)).toBeInTheDocument();
    });
  });

  it('shows loading state during password reset request', async () => {
    const user = userEvent.setup();
    
    // Override the forgot password endpoint to add delay
    server.use(
      rest.post('http://localhost:8080/api/v1/auth/forgot-password', (req, res, ctx) => {
        return res(
          ctx.delay(1000),
          ctx.status(200),
          ctx.json({ message: 'Password reset email sent successfully' })
        );
      })
    );
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    // Check for loading indicator
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
  });

  it('allows multiple password reset requests', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    // First request
    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/password reset instructions have been sent/i)).toBeInTheDocument();
    });

    // Clear the field and make another request
    await user.clear(emailField);
    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/password reset instructions have been sent/i)).toBeInTheDocument();
    });
  });

  it('validates email format before making API request', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    // Enter invalid email format
    await user.type(emailField, 'invalid-email-format');
    await user.click(submitButton);

    // Should show validation error without making API request
    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
    });

    // No success or server error message should appear
    expect(screen.queryByText(/password reset instructions have been sent/i)).not.toBeInTheDocument();
    expect(screen.queryByText(/user not found/i)).not.toBeInTheDocument();
  });

  it('clears previous messages when submitting new request', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    // First, trigger an error
    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/user not found/i)).toBeInTheDocument();
    });

    // Then make a successful request
    await user.clear(emailField);
    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.queryByText(/user not found/i)).not.toBeInTheDocument();
      expect(screen.getByText(/password reset instructions have been sent/i)).toBeInTheDocument();
    });
  });

  it('handles network timeout gracefully', async () => {
    const user = userEvent.setup();
    
    // Override the forgot password endpoint to simulate timeout
    server.use(
      rest.post('http://localhost:8080/api/v1/auth/forgot-password', (req, res, ctx) => {
        return res(
          ctx.delay(15000), // Longer than axios timeout
          ctx.status(200),
          ctx.json({ message: 'Password reset email sent successfully' })
        );
      })
    );
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    // Should eventually show an error (timeout or network error)
    await waitFor(() => {
      expect(screen.getByText(/failed to send password reset email/i)).toBeInTheDocument();
    }, { timeout: 15000 });
  });
});
