import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import ForgotPassword from './ForgotPassword';

const theme = createTheme();

// Mock the auth context
const mockForgotPassword = jest.fn();
const mockAuthContext = {
  forgotPassword: mockForgotPassword,
  loading: false,
  isAuthenticated: false,
  user: null,
  login: jest.fn(),
  signup: jest.fn(),
  logout: jest.fn(),
};

jest.mock('../../context/AuthContext', () => ({
  useAuth: () => mockAuthContext,
}));

const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </ThemeProvider>
);

describe('ForgotPassword Component - Unit Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders forgot password form with all required elements', () => {
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    expect(screen.getByRole('heading', { name: /forgot password/i })).toBeInTheDocument();
    expect(screen.getByText(/enter your email address and we'll send you instructions/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /send reset instructions/i })).toBeInTheDocument();
  });

  it('displays validation error for empty email field', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    });
  });

  it('displays validation error for invalid email format', async () => {
    const user = userEvent.setup();
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    await user.type(emailField, 'invalid-email');

    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email/i)).toBeInTheDocument();
    });
  });

  it('calls forgotPassword function with correct email on valid form submission', async () => {
    const user = userEvent.setup();
    mockForgotPassword.mockResolvedValue({ success: true });
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockForgotPassword).toHaveBeenCalledWith('<EMAIL>');
    });
  });

  it('displays success message when password reset request succeeds', async () => {
    const user = userEvent.setup();
    mockForgotPassword.mockResolvedValue({ success: true });
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/password reset instructions have been sent/i)).toBeInTheDocument();
    });
  });

  it('displays error message when password reset request fails', async () => {
    const user = userEvent.setup();
    const errorMessage = 'User not found';
    mockForgotPassword.mockRejectedValue(new Error(errorMessage));
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(errorMessage)).toBeInTheDocument();
    });
  });

  it('disables submit button when form is submitting', async () => {
    const user = userEvent.setup();
    mockForgotPassword.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    expect(submitButton).toBeDisabled();
  });

  it('shows loading indicator when form is submitting', async () => {
    const user = userEvent.setup();
    mockForgotPassword.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 1000)));
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('contains navigation links to login and signup pages', () => {
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    expect(screen.getByText(/back to sign in/i)).toBeInTheDocument();
    expect(screen.getByText(/don't have an account\? sign up/i)).toBeInTheDocument();
  });

  it('clears error message when user starts typing after error', async () => {
    const user = userEvent.setup();
    mockForgotPassword.mockRejectedValue(new Error('User not found'));
    
    render(
      <TestWrapper>
        <ForgotPassword />
      </TestWrapper>
    );

    const emailField = screen.getByLabelText(/email address/i);
    const submitButton = screen.getByRole('button', { name: /send reset instructions/i });

    // First, trigger an error
    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/user not found/i)).toBeInTheDocument();
    });

    // Clear the error by changing the email and submitting again
    mockForgotPassword.mockResolvedValue({ success: true });
    await user.clear(emailField);
    await user.type(emailField, '<EMAIL>');
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.queryByText(/user not found/i)).not.toBeInTheDocument();
      expect(screen.getByText(/password reset instructions have been sent/i)).toBeInTheDocument();
    });
  });
});
