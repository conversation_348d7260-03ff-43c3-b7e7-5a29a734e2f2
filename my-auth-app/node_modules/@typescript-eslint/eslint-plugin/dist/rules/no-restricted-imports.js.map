{"version": 3, "file": "no-restricted-imports.js", "sourceRoot": "", "sources": ["../../src/rules/no-restricted-imports.ts"], "names": [], "mappings": ";;;;;AAMA,oDAA4B;AAM5B,kCAAgD;AAChD,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,uBAAuB,CAAC,CAAC;AAK5D,MAAM,4BAA4B,GAAG;IACnC,gBAAgB,EAAE;QAChB,IAAI,EAAE,SAAS;QACf,OAAO,EAAE,KAAK;KACf;CACF,CAAC;AACF,MAAM,qCAAqC,GAAG;IAC5C,KAAK,EAAE;QACL,KAAK,EAAE;YACL,EAAE;YACF;gBACE,UAAU,EAAE,4BAA4B;aACzC;SACF;KACF;CACF,CAAC;AACF,MAAM,4CAA4C,GAAG;IACnD,KAAK,EAAE;QACL,EAAE;QACF;YACE,KAAK,EAAE;gBACL,UAAU,EAAE,4BAA4B;aACzC;SACF;KACF;CACF,CAAC;AACF,MAAM,MAAM,GAAG,IAAA,gBAAS,oBACjB,QAAQ,CAAC,IAAI,CAAC,MAAM,GACzB;IACE,KAAK,EAAE;QACL,qCAAqC;QACrC;YACE,KAAK,EAAE;gBACL,UAAU,EAAE;oBACV,KAAK,EAAE,qCAAqC;oBAC5C,QAAQ,EAAE,4CAA4C;iBACvD;aACF;SACF;KACF;CACF,CACF,CAAC;AAEF,SAAS,eAAe,CACtB,GAAY;IAEZ,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;AAC5D,CAAC;AAED,SAAS,kBAAkB,CACzB,GAAY;IAEZ,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,UAAU,CAAC,CAAC;AAC/D,CAAC;AAED,SAAS,8BAA8B,CACrC,OAAgB;IAEhB,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/B,OAAO,KAAK,CAAC;KACd;IACD,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QAClC,OAAO,KAAK,CAAC;KACd;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,kBAAkB,CAAC,OAAgB;IAC1C,IAAI,8BAA8B,CAAC,OAAO,CAAC,EAAE;QAC3C,OAAO,OAAO,CAAC;KAChB;IACD,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QAC/B,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;KACzB;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,SAAS,qBAAqB,CAC5B,OAAgB;IAEhB,IAAI,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAE;QAClC,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;KAC5B;IACD,OAAO,EAAE,CAAC;AACZ,CAAC;AAED,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,uBAAuB;IAC7B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,oDAAoD;YACjE,WAAW,EAAE,KAAK;YAClB,eAAe,EAAE,IAAI;SACtB;QACD,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAChC,OAAO,EAAE,QAAQ,CAAC,IAAI,CAAC,OAAO;QAC9B,MAAM;KACP;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAE5B,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACxB,OAAO,EAAE,CAAC;SACX;QAED,MAAM,eAAe,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;QACpD,MAAM,4BAA4B,GAAgB,IAAI,GAAG,EAAE,CAAC;QAC5D,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE;YAC5C,IACE,OAAO,cAAc,KAAK,QAAQ;gBAClC,cAAc,CAAC,gBAAgB,EAC/B;gBACA,4BAA4B,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;aACvD;SACF;QACD,SAAS,uBAAuB,CAAC,YAAoB;YACnD,OAAO,4BAA4B,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,kBAAkB,GAAG,qBAAqB,CAAC,OAAO,CAAC,CAAC;QAC1D,MAAM,yBAAyB,GAAa,EAAE,CAAC;QAC/C,KAAK,MAAM,iBAAiB,IAAI,kBAAkB,EAAE;YAClD,IACE,OAAO,iBAAiB,KAAK,QAAQ;gBACrC,iBAAiB,CAAC,gBAAgB,EAClC;gBACA,sDAAsD;gBACtD,yBAAyB,CAAC,IAAI,CAC5B,IAAA,gBAAM,EAAC;oBACL,kBAAkB,EAAE,IAAI;oBACxB,UAAU,EAAE,CAAC,iBAAiB,CAAC,aAAa;iBAC7C,CAAC,CAAC,GAAG,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAChC,CAAC;aACH;SACF;QACD,SAAS,0BAA0B,CAAC,YAAoB;YACtD,OAAO;YACL,kEAAkE;YAClE,yBAAyB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CACzE,CAAC;QACJ,CAAC;QAED,OAAO;YACL,iBAAiB,CAAC,IAAI;gBACpB,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;oBAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC9C,IACE,CAAC,uBAAuB,CAAC,YAAY,CAAC;wBACtC,CAAC,0BAA0B,CAAC,YAAY,CAAC,EACzC;wBACA,OAAO,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;qBACtC;iBACF;qBAAM;oBACL,OAAO,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;iBACtC;YACH,CAAC;YACD,gCAAgC,CAC9B,IAEC;gBAED,IAAI,IAAI,CAAC,UAAU,KAAK,MAAM,EAAE;oBAC9B,MAAM,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBAC9C,IACE,CAAC,uBAAuB,CAAC,YAAY,CAAC;wBACtC,CAAC,0BAA0B,CAAC,YAAY,CAAC,EACzC;wBACA,OAAO,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;qBAC3C;iBACF;qBAAM;oBACL,OAAO,KAAK,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;iBAC3C;YACH,CAAC;YACD,oBAAoB,EAAE,KAAK,CAAC,oBAAoB;SACjD,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}